<%@page contentType="application/rtf" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>

<%
    // Set response headers for Word download
    response.setContentType("application/rtf");
    response.setHeader("Content-Disposition", "attachment; filename=Data_Layanan_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".rtf");
    
    try {
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        
        // Mulai RTF document
        out.print("{\\rtf1\\ansi\\deff0 ");
        out.print("{\\fonttbl{\\f0 Times New Roman;}{\\f1 Arial;}{\\f2 Calibri;}}");
        out.print("{\\colortbl;\\red0\\green0\\blue0;\\red255\\green255\\blue255;\\red0\\green0\\blue255;\\red255\\green0\\blue0;\\red128\\green128\\blue128;}");
        
        // Page setup
        out.print("\\paperw12240\\paperh15840\\margl1440\\margr1440\\margt1440\\margb1440");
        
        // Document title
        out.print("\\f2\\fs32\\b\\qc DATA LAYANAN ARQETA\\par");
        out.print("\\f2\\fs18\\qc\\cf5 Diekspor pada: " + dateFormat.format(new Date()) + "\\par\\par");
        
        // Reset formatting
        out.print("\\f0\\fs22\\b0\\ql");
        
        // Query untuk mengambil semua data layanan
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
        ResultSet rs = ps.executeQuery();
        
        // Hitung total records
        PreparedStatement countPs = conn.prepareStatement("SELECT COUNT(*) as total FROM services");
        ResultSet countRs = countPs.executeQuery();
        int totalRecords = 0;
        if (countRs.next()) {
            totalRecords = countRs.getInt("total");
        }
        countRs.close();
        countPs.close();
        
        out.print("\\b Total Layanan: " + totalRecords + "\\b0\\par\\par");
        
        // Table header
        out.print("{\\trowd\\trgaph108\\trleft-108");
        out.print("\\cellx1000\\cellx3500\\cellx5000\\cellx6500\\cellx8500\\cellx10500");
        out.print("\\intbl\\b\\qc No\\cell");
        out.print("\\intbl\\b\\qc Nama Layanan\\cell");
        out.print("\\intbl\\b\\qc Gambar\\cell");
        out.print("\\intbl\\b\\qc Jumlah\\cell");
        out.print("\\intbl\\b\\qc Harga\\cell");
        out.print("\\intbl\\b\\qc Tanggal Dibuat\\cell");
        out.print("\\row}");
        
        int rowNumber = 1;
        while (rs.next()) {
            int id = rs.getInt("id");
            String name = rs.getString("name");
            String images = rs.getString("images");
            int quantity = rs.getInt("quantity");
            double price = rs.getDouble("price");
            String createdAt = rs.getString("created_at");
            
            // Parse and format date
            String formattedDate = createdAt;
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(createdAt);
                formattedDate = dateFormat.format(date);
            } catch (Exception e) {
                // Keep original format if parsing fails
            }
            
            // Escape RTF special characters
            name = name.replace("\\", "\\\\").replace("{", "\\{").replace("}", "\\}");
            
            // Table row
            out.print("{\\trowd\\trgaph108\\trleft-108");
            out.print("\\cellx1000\\cellx3500\\cellx5000\\cellx6500\\cellx8500\\cellx10500");
            out.print("\\intbl\\qc " + rowNumber + "\\cell");
            out.print("\\intbl\\ql " + name + "\\cell");
            
            // Image cell
            if (images != null && !images.isEmpty()) {
                out.print("\\intbl\\qc " + images + "\\cell");
            } else {
                out.print("\\intbl\\qc -\\cell");
            }
            
            out.print("\\intbl\\qc " + quantity + "\\cell");
            out.print("\\intbl\\qr Rp " + priceFormat.format(price) + "\\cell");
            out.print("\\intbl\\qc " + formattedDate + "\\cell");
            out.print("\\row}");
            
            rowNumber++;
        }
        
        // Summary section
        out.print("\\par\\par");
        out.print("\\b RINGKASAN:\\b0\\par");
        
        // Calculate totals
        PreparedStatement summaryPs = conn.prepareStatement(
            "SELECT COUNT(*) as total_services, SUM(quantity) as total_quantity, " +
            "SUM(price * quantity) as total_value FROM services"
        );
        ResultSet summaryRs = summaryPs.executeQuery();
        
        if (summaryRs.next()) {
            int totalServices = summaryRs.getInt("total_services");
            int totalQuantity = summaryRs.getInt("total_quantity");
            double totalValue = summaryRs.getDouble("total_value");
            
            out.print("Total Jenis Layanan: " + totalServices + "\\par");
            out.print("Total Kuantitas: " + totalQuantity + "\\par");
            out.print("Total Nilai Inventori: Rp " + priceFormat.format(totalValue) + "\\par");
        }
        
        summaryRs.close();
        summaryPs.close();
        
        // Footer
        out.print("\\par\\par");
        out.print("\\qc\\fs16 Dokumen ini digenerate secara otomatis oleh Sistem Dashboard Admin Arqeta\\par");
        out.print("\\qc\\fs16 " + new SimpleDateFormat("EEEE, dd MMMM yyyy 'pukul' HH:mm:ss").format(new Date()));
        
        // Close RTF document
        out.print("}");
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Database Error: " + e.getMessage());
    } catch (Exception e) {
        // General error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Export Error: " + e.getMessage());
    }
%>
