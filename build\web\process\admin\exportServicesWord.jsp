<%@page contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>
<%@page import="java.io.*"%>
<%@page import="java.util.zip.*"%>
<%
    // Set response headers for DOCX download
    response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    response.setHeader("Content-Disposition", "attachment; filename=Data_Layanan_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".docx");
    
    try {
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        
        // Query untuk mengambil semua data layanan
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
        ResultSet rs = ps.executeQuery();
        
        // Hitung total records dan statistik
        PreparedStatement summaryPs = conn.prepareStatement(
            "SELECT COUNT(*) as total_services, SUM(quantity) as total_quantity, " +
            "SUM(price * quantity) as total_value FROM services"
        );
        ResultSet summaryRs = summaryPs.executeQuery();
        
        int totalServices = 0;
        int totalQuantity = 0;
        double totalValue = 0;
        
        if (summaryRs.next()) {
            totalServices = summaryRs.getInt("total_services");
            totalQuantity = summaryRs.getInt("total_quantity");
            totalValue = summaryRs.getDouble("total_value");
        }
        summaryRs.close();
        summaryPs.close();
        
        // Create DOCX content using Office Open XML format
        ByteArrayOutputStream docxOutput = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(docxOutput);
        
        // Add [Content_Types].xml
        zipOut.putNextEntry(new ZipEntry("[Content_Types].xml"));
        String contentTypes = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Types xmlns=\"http://schemas.openxmlformats.org/package/2006/content-types\">" +
            "<Default Extension=\"rels\" ContentType=\"application/vnd.openxmlformats-package.relationships+xml\"/>" +
            "<Default Extension=\"xml\" ContentType=\"application/xml\"/>" +
            "<Override PartName=\"/word/document.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml\"/>" +
            "</Types>";
        zipOut.write(contentTypes.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add _rels/.rels
        zipOut.putNextEntry(new ZipEntry("_rels/.rels"));
        String rels = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">" +
            "<Relationship Id=\"rId1\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\" Target=\"word/document.xml\"/>" +
            "</Relationships>";
        zipOut.write(rels.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add word/_rels/document.xml.rels
        zipOut.putNextEntry(new ZipEntry("word/_rels/document.xml.rels"));
        String docRels = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">" +
            "</Relationships>";
        zipOut.write(docRels.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Create main document content
        StringBuilder docContent = new StringBuilder();
        docContent.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
        docContent.append("<w:document xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\">");
        docContent.append("<w:body>");
        
        // Document title
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:b/><w:sz w:val=\"32\"/></w:rPr>");
        docContent.append("<w:t>DATA LAYANAN ARQETA</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Export date
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"18\"/><w:color w:val=\"808080\"/></w:rPr>");
        docContent.append("<w:t>Diekspor pada: ").append(dateFormat.format(new Date())).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Empty line
        docContent.append("<w:p></w:p>");
        
        // Total services info
        docContent.append("<w:p>");
        docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
        docContent.append("<w:t>Total Layanan: ").append(totalServices).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Empty line
        docContent.append("<w:p></w:p>");
        
        // Table
        docContent.append("<w:tbl>");
        docContent.append("<w:tblPr>");
        docContent.append("<w:tblStyle w:val=\"TableGrid\"/>");
        docContent.append("<w:tblW w:w=\"0\" w:type=\"auto\"/>");
        docContent.append("<w:jc w:val=\"center\"/>");
        docContent.append("<w:tblBorders>");
        docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:insideH w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:insideV w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("</w:tblBorders>");
        docContent.append("</w:tblPr>");
        
        // Table header
        docContent.append("<w:tr>");
        String[] headers = {"No", "Nama Layanan", "Gambar", "Jumlah", "Harga", "Tanggal Dibuat"};
        for (String header : headers) {
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
            docContent.append("<w:t>").append(escapeXml(header)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");
        }
        docContent.append("</w:tr>");
        
        // Table rows
        int rowNumber = 1;
        while (rs.next()) {
            int id = rs.getInt("id");
            String name = rs.getString("name");
            String images = rs.getString("images");
            int quantity = rs.getInt("quantity");
            double price = rs.getDouble("price");
            String createdAt = rs.getString("created_at");
            
            // Parse and format date
            String formattedDate = createdAt;
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(createdAt);
                formattedDate = dateFormat.format(date);
            } catch (Exception e) {
                // Keep original format if parsing fails
            }
            
            docContent.append("<w:tr>");
            
            // Cell data
            String[] cellData = {
                String.valueOf(rowNumber),
                name,
                images != null ? images : "-",
                String.valueOf(quantity),
                "Rp " + priceFormat.format(price),
                formattedDate
            };
            
            String[] alignments = {"center", "left", "center", "center", "right", "center"};
            
            for (int i = 0; i < cellData.length; i++) {
                docContent.append("<w:tc>");
                docContent.append("<w:tcPr>");
                docContent.append("<w:tcBorders>");
                docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
                docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
                docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
                docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
                docContent.append("</w:tcBorders>");
                docContent.append("</w:tcPr>");
                docContent.append("<w:p>");
                docContent.append("<w:pPr><w:jc w:val=\"").append(alignments[i]).append("\"/></w:pPr>");
                docContent.append("<w:r>");
                docContent.append("<w:t>").append(escapeXml(cellData[i])).append("</w:t></w:r>");
                docContent.append("</w:p>");
                docContent.append("</w:tc>");
            }
            
            docContent.append("</w:tr>");
            rowNumber++;
        }
        
        docContent.append("</w:tbl>");
        
        // Summary section
        docContent.append("<w:p></w:p>");
        docContent.append("<w:p>");
        docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
        docContent.append("<w:t>RINGKASAN:</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Jenis Layanan: ").append(totalServices).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Kuantitas: ").append(totalQuantity).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Nilai Inventori: Rp ").append(priceFormat.format(totalValue)).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Footer
        docContent.append("<w:p></w:p>");
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"16\"/></w:rPr>");
        docContent.append("<w:t>Dokumen ini digenerate secara otomatis oleh Sistem Dashboard Admin Arqeta</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"16\"/></w:rPr>");
        docContent.append("<w:t>").append(new SimpleDateFormat("EEEE, dd MMMM yyyy 'pukul' HH:mm:ss").format(new Date())).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("</w:body>");
        docContent.append("</w:document>");
        
        // Add word/document.xml
        zipOut.putNextEntry(new ZipEntry("word/document.xml"));
        zipOut.write(docContent.toString().getBytes("UTF-8"));
        zipOut.closeEntry();
        
        zipOut.close();
        
        // Write DOCX to response
        byte[] docxBytes = docxOutput.toByteArray();
        response.getOutputStream().write(docxBytes);
        response.getOutputStream().flush();
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Database Error: " + e.getMessage());
    } catch (Exception e) {
        // General error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Export Error: " + e.getMessage());
    }
%>

<%!
    // Helper method untuk escape XML characters
    private String escapeXml(String str) {
        if (str == null) return "";
        return str.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&apos;");
    }
%>
