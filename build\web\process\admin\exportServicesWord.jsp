<%@page contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>
<%@page import="java.io.*"%>
<%@page import="java.util.zip.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="java.util.Base64"%>
<%
    // Set response headers for DOCX download
    response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    response.setHeader("Content-Disposition", "attachment; filename=Data_Layanan_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".docx");
    
    try {
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        
        // Query untuk mengambil semua data layanan
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
        ResultSet rs = ps.executeQuery();
        
        // Hitung total records dan statistik
        PreparedStatement summaryPs = conn.prepareStatement(
            "SELECT COUNT(*) as total_services, SUM(quantity) as total_quantity, " +
            "SUM(price * quantity) as total_value FROM services"
        );
        ResultSet summaryRs = summaryPs.executeQuery();
        
        int totalServices = 0;
        int totalQuantity = 0;
        double totalValue = 0;
        
        if (summaryRs.next()) {
            totalServices = summaryRs.getInt("total_services");
            totalQuantity = summaryRs.getInt("total_quantity");
            totalValue = summaryRs.getDouble("total_value");
        }
        summaryRs.close();
        summaryPs.close();
        
        // Create DOCX content using Office Open XML format
        ByteArrayOutputStream docxOutput = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(docxOutput);

        // Collect unique images first
        PreparedStatement imagePs = conn.prepareStatement("SELECT DISTINCT images FROM services WHERE images IS NOT NULL AND images != ''");
        ResultSet imageRs = imagePs.executeQuery();
        java.util.List<String> imageList = new java.util.ArrayList<>();
        while (imageRs.next()) {
            String imageName = imageRs.getString("images");
            if (imageName != null && !imageName.trim().isEmpty()) {
                imageList.add(imageName);
            }
        }
        imageRs.close();
        imagePs.close();

        // Add [Content_Types].xml
        zipOut.putNextEntry(new ZipEntry("[Content_Types].xml"));
        StringBuilder contentTypes = new StringBuilder();
        contentTypes.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
        contentTypes.append("<Types xmlns=\"http://schemas.openxmlformats.org/package/2006/content-types\">");
        contentTypes.append("<Default Extension=\"rels\" ContentType=\"application/vnd.openxmlformats-package.relationships+xml\"/>");
        contentTypes.append("<Default Extension=\"xml\" ContentType=\"application/xml\"/>");
        contentTypes.append("<Override PartName=\"/word/document.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml\"/>");

        // Add content types for images
        for (String imageName : imageList) {
            String extension = getImageExtension(imageName);
            String contentType = getImageContentType(extension);
            contentTypes.append("<Default Extension=\"").append(extension).append("\" ContentType=\"").append(contentType).append("\"/>");
        }

        contentTypes.append("</Types>");
        zipOut.write(contentTypes.toString().getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add _rels/.rels
        zipOut.putNextEntry(new ZipEntry("_rels/.rels"));
        String rels = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">" +
            "<Relationship Id=\"rId1\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\" Target=\"word/document.xml\"/>" +
            "</Relationships>";
        zipOut.write(rels.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add word/_rels/document.xml.rels
        zipOut.putNextEntry(new ZipEntry("word/_rels/document.xml.rels"));
        StringBuilder docRels = new StringBuilder();
        docRels.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
        docRels.append("<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">");

        // Add relationships for images
        for (int i = 0; i < imageList.size(); i++) {
            String imageName = imageList.get(i);
            docRels.append("<Relationship Id=\"rId").append(i + 2).append("\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\" Target=\"media/").append(imageName).append("\"/>");
        }

        docRels.append("</Relationships>");
        zipOut.write(docRels.toString().getBytes("UTF-8"));
        zipOut.closeEntry();

        // Add images to media folder
        for (String imageName : imageList) {
            String imageBase64 = getImageBase64(imageName, getServletContext());
            if (imageBase64 != null) {
                zipOut.putNextEntry(new ZipEntry("word/media/" + imageName));
                byte[] imageBytes = Base64.getDecoder().decode(imageBase64);
                zipOut.write(imageBytes);
                zipOut.closeEntry();
            }
        }
        
        // Create main document content
        StringBuilder docContent = new StringBuilder();
        docContent.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
        docContent.append("<w:document xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\" ");
        docContent.append("xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" ");
        docContent.append("xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\" ");
        docContent.append("xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\" ");
        docContent.append("xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\">");
        docContent.append("<w:body>");
        
        // Document title
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:b/><w:sz w:val=\"32\"/></w:rPr>");
        docContent.append("<w:t>DATA LAYANAN ARQETA</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Export date
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"18\"/><w:color w:val=\"808080\"/></w:rPr>");
        docContent.append("<w:t>Diekspor pada: ").append(dateFormat.format(new Date())).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Empty line
        docContent.append("<w:p></w:p>");
        
        // Total services info
        docContent.append("<w:p>");
        docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
        docContent.append("<w:t>Total Layanan: ").append(totalServices).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Empty line
        docContent.append("<w:p></w:p>");
        
        // Table
        docContent.append("<w:tbl>");
        docContent.append("<w:tblPr>");
        docContent.append("<w:tblStyle w:val=\"TableGrid\"/>");
        docContent.append("<w:tblW w:w=\"0\" w:type=\"auto\"/>");
        docContent.append("<w:jc w:val=\"center\"/>");
        docContent.append("<w:tblBorders>");
        docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:insideH w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("<w:insideV w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
        docContent.append("</w:tblBorders>");
        docContent.append("</w:tblPr>");
        
        // Table header
        docContent.append("<w:tr>");
        String[] headers = {"ID", "Nama Layanan", "Gambar", "Jumlah", "Harga", "Tanggal Dibuat"};
        for (String header : headers) {
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
            docContent.append("<w:t>").append(escapeXml(header)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");
        }
        docContent.append("</w:tr>");
        
        // Table rows
        while (rs.next()) {
            int id = rs.getInt("id");
            String name = rs.getString("name");
            String images = rs.getString("images");
            int quantity = rs.getInt("quantity");
            double price = rs.getDouble("price");
            String createdAt = rs.getString("created_at");

            // Parse and format date
            String formattedDate = createdAt;
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(createdAt);
                formattedDate = dateFormat.format(date);
            } catch (Exception e) {
                // Keep original format if parsing fails
            }

            docContent.append("<w:tr>");

            // ID Cell
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            docContent.append("<w:r>");
            docContent.append("<w:t>").append(String.valueOf(id)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            // Nama Layanan Cell
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"left\"/></w:pPr>");
            docContent.append("<w:r>");
            docContent.append("<w:t>").append(escapeXml(name)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            // Gambar Cell - dengan gambar yang sebenarnya
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            if (images != null && !images.trim().isEmpty()) {
                // Cari index gambar dalam imageList
                int imageIndex = imageList.indexOf(images);
                if (imageIndex >= 0) {
                    // Menampilkan gambar yang sebenarnya
                    docContent.append("<w:r>");
                    docContent.append("<w:drawing>");
                    docContent.append("<wp:inline distT=\"0\" distB=\"0\" distL=\"0\" distR=\"0\" xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\">");
                    docContent.append("<wp:extent cx=\"1905000\" cy=\"1270000\"/>"); // 5cm x 3.35cm
                    docContent.append("<wp:effectExtent l=\"0\" t=\"0\" r=\"0\" b=\"0\"/>");
                    docContent.append("<wp:docPr id=\"").append(id).append("\" name=\"").append(escapeXml(images)).append("\"/>");
                    docContent.append("<wp:cNvGraphicFramePr>");
                    docContent.append("<a:graphicFrameLocks xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\" noChangeAspect=\"1\"/>");
                    docContent.append("</wp:cNvGraphicFramePr>");
                    docContent.append("<a:graphic xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\">");
                    docContent.append("<a:graphicData uri=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">");
                    docContent.append("<pic:pic xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">");
                    docContent.append("<pic:nvPicPr>");
                    docContent.append("<pic:cNvPr id=\"").append(id).append("\" name=\"").append(escapeXml(images)).append("\"/>");
                    docContent.append("<pic:cNvPicPr/>");
                    docContent.append("</pic:nvPicPr>");
                    docContent.append("<pic:blipFill>");
                    docContent.append("<a:blip r:embed=\"rId").append(imageIndex + 2).append("\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\"/>");
                    docContent.append("<a:stretch>");
                    docContent.append("<a:fillRect/>");
                    docContent.append("</a:stretch>");
                    docContent.append("</pic:blipFill>");
                    docContent.append("<pic:spPr>");
                    docContent.append("<a:xfrm>");
                    docContent.append("<a:off x=\"0\" y=\"0\"/>");
                    docContent.append("<a:ext cx=\"1905000\" cy=\"1270000\"/>");
                    docContent.append("</a:xfrm>");
                    docContent.append("<a:prstGeom prst=\"rect\">");
                    docContent.append("<a:avLst/>");
                    docContent.append("</a:prstGeom>");
                    docContent.append("</pic:spPr>");
                    docContent.append("</pic:pic>");
                    docContent.append("</a:graphicData>");
                    docContent.append("</a:graphic>");
                    docContent.append("</wp:inline>");
                    docContent.append("</w:drawing>");
                    docContent.append("</w:r>");
                } else {
                    // Fallback jika gambar tidak ditemukan
                    docContent.append("<w:r>");
                    docContent.append("<w:t>📷 ").append(escapeXml(images)).append("</w:t></w:r>");
                }
            } else {
                docContent.append("<w:r><w:rPr><w:color w:val=\"808080\"/></w:rPr>");
                docContent.append("<w:t>-</w:t></w:r>");
            }
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            // Jumlah Cell
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            docContent.append("<w:r>");
            docContent.append("<w:t>").append(String.valueOf(quantity)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            // Harga Cell
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"right\"/></w:pPr>");
            docContent.append("<w:r>");
            docContent.append("<w:t>Rp ").append(priceFormat.format(price)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            // Tanggal Dibuat Cell
            docContent.append("<w:tc>");
            docContent.append("<w:tcPr>");
            docContent.append("<w:tcBorders>");
            docContent.append("<w:top w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:left w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:bottom w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("<w:right w:val=\"single\" w:sz=\"4\" w:space=\"0\" w:color=\"000000\"/>");
            docContent.append("</w:tcBorders>");
            docContent.append("</w:tcPr>");
            docContent.append("<w:p>");
            docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
            docContent.append("<w:r>");
            docContent.append("<w:t>").append(escapeXml(formattedDate)).append("</w:t></w:r>");
            docContent.append("</w:p>");
            docContent.append("</w:tc>");

            docContent.append("</w:tr>");
        }
        
        docContent.append("</w:tbl>");
        
        // Summary section
        docContent.append("<w:p></w:p>");
        docContent.append("<w:p>");
        docContent.append("<w:r><w:rPr><w:b/></w:rPr>");
        docContent.append("<w:t>RINGKASAN:</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Jenis Layanan: ").append(totalServices).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Kuantitas: ").append(totalQuantity).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:r>");
        docContent.append("<w:t>Total Nilai Inventori: Rp ").append(priceFormat.format(totalValue)).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        // Footer
        docContent.append("<w:p></w:p>");
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"16\"/></w:rPr>");
        docContent.append("<w:t>Dokumen ini digenerate secara otomatis oleh Sistem Dashboard Admin Arqeta</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("<w:p>");
        docContent.append("<w:pPr><w:jc w:val=\"center\"/></w:pPr>");
        docContent.append("<w:r><w:rPr><w:sz w:val=\"16\"/></w:rPr>");
        docContent.append("<w:t>").append(new SimpleDateFormat("EEEE, dd MMMM yyyy 'pukul' HH:mm:ss").format(new Date())).append("</w:t></w:r>");
        docContent.append("</w:p>");
        
        docContent.append("</w:body>");
        docContent.append("</w:document>");
        
        // Add word/document.xml
        zipOut.putNextEntry(new ZipEntry("word/document.xml"));
        zipOut.write(docContent.toString().getBytes("UTF-8"));
        zipOut.closeEntry();
        
        zipOut.close();
        
        // Write DOCX to response
        byte[] docxBytes = docxOutput.toByteArray();
        response.getOutputStream().write(docxBytes);
        response.getOutputStream().flush();
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Database Error: " + e.getMessage());
    } catch (Exception e) {
        // General error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Export Error: " + e.getMessage());
    }
%>

<%!
    // Helper method untuk escape XML characters
    private String escapeXml(String str) {
        if (str == null) return "";
        return str.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&apos;");
    }

    // Helper method untuk membaca gambar dan convert ke base64
    private String getImageBase64(String imagePath, ServletContext context) {
        try {
            String realPath = context.getRealPath("/dist/img/" + imagePath);
            File imageFile = new File(realPath);
            if (imageFile.exists()) {
                byte[] imageBytes = Files.readAllBytes(imageFile.toPath());
                return Base64.getEncoder().encodeToString(imageBytes);
            }
        } catch (Exception e) {
            // Ignore error, return null
        }
        return null;
    }

    // Helper method untuk mendapatkan extension file
    private String getImageExtension(String filename) {
        if (filename == null) return "png";
        int lastDot = filename.lastIndexOf('.');
        if (lastDot > 0) {
            return filename.substring(lastDot + 1).toLowerCase();
        }
        return "png";
    }

    // Helper method untuk mendapatkan content type gambar
    private String getImageContentType(String extension) {
        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            default:
                return "image/png";
        }
    }
%>
