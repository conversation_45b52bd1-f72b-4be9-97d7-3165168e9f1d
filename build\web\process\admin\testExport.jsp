<%--
    Document   : testExport
    Created on : July 30, 2025
    Author     : Arqeta
    Description: Test page for export functionality
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Export Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Export Functionality</h1>
    
    <div class="test-section info">
        <h3>Database Connection Test</h3>
        <%
            try {
                PreparedStatement testPs = conn.prepareStatement("SELECT COUNT(*) as total FROM services");
                ResultSet testRs = testPs.executeQuery();
                if (testRs.next()) {
                    int total = testRs.getInt("total");
                    out.println("<p>✓ Database connection successful</p>");
                    out.println("<p>✓ Found " + total + " services in database</p>");
                } else {
                    out.println("<p>✗ No data found in services table</p>");
                }
                testRs.close();
                testPs.close();
            } catch (Exception e) {
                out.println("<p>✗ Database connection failed: " + e.getMessage() + "</p>");
            }
        %>
    </div>
    
    <div class="test-section info">
        <h3>Export Links Test</h3>
        <p>Click the buttons below to test export functionality:</p>
        <button onclick="testJSONExport()">Test JSON Export</button>
        <button onclick="testWordExport()">Test Word Export</button>
        
        <div id="testResults" style="margin-top: 15px;"></div>
    </div>
    
    <div class="test-section info">
        <h3>Sample Data Preview</h3>
        <%
            try {
                PreparedStatement samplePs = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC LIMIT 3");
                ResultSet sampleRs = samplePs.executeQuery();
                
                out.println("<table border='1' style='border-collapse: collapse; width: 100%;'>");
                out.println("<tr><th>ID</th><th>Name</th><th>Image</th><th>Quantity</th><th>Price</th></tr>");
                
                while (sampleRs.next()) {
                    out.println("<tr>");
                    out.println("<td>" + sampleRs.getInt("id") + "</td>");
                    out.println("<td>" + sampleRs.getString("name") + "</td>");
                    out.println("<td>" + sampleRs.getString("images") + "</td>");
                    out.println("<td>" + sampleRs.getInt("quantity") + "</td>");
                    out.println("<td>Rp " + String.format("%,.0f", sampleRs.getDouble("price")) + "</td>");
                    out.println("</tr>");
                }
                
                out.println("</table>");
                sampleRs.close();
                samplePs.close();
            } catch (Exception e) {
                out.println("<p>Error loading sample data: " + e.getMessage() + "</p>");
            }
        %>
    </div>
    
    <script>
        function testJSONExport() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p>Testing JSON export...</p>';
            
            // Test if the export URL is accessible
            fetch('exportServicesJSON.jsp')
                .then(response => {
                    if (response.ok) {
                        resultsDiv.innerHTML += '<p style="color: green;">✓ JSON export endpoint is accessible</p>';
                        resultsDiv.innerHTML += '<p><a href="exportServicesJSON.jsp" target="_blank">Download JSON Export</a></p>';
                    } else {
                        resultsDiv.innerHTML += '<p style="color: red;">✗ JSON export endpoint returned error: ' + response.status + '</p>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML += '<p style="color: red;">✗ JSON export test failed: ' + error.message + '</p>';
                });
        }
        
        function testWordExport() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p>Testing Word export...</p>';
            
            // Test if the export URL is accessible
            fetch('exportServicesWord.jsp')
                .then(response => {
                    if (response.ok) {
                        resultsDiv.innerHTML += '<p style="color: green;">✓ Word export endpoint is accessible</p>';
                        resultsDiv.innerHTML += '<p><a href="exportServicesWord.jsp" target="_blank">Download Word Export</a></p>';
                    } else {
                        resultsDiv.innerHTML += '<p style="color: red;">✗ Word export endpoint returned error: ' + response.status + '</p>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML += '<p style="color: red;">✗ Word export test failed: ' + error.message + '</p>';
                });
        }
    </script>
</body>
</html>
