<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Export Functions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Test Export Functions - Services Data</h1>
    
    <div class="test-section">
        <h3>JSON Export Test</h3>
        <p>Test ekspor data layanan ke format JSON dengan sintaksis yang benar dan formatting yang rapi.</p>
        <button class="btn" onclick="testJSONExport()">Test JSON Export</button>
        <div id="json-result" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h3>Word DOCX Export Test</h3>
        <p>Test ekspor data layanan ke format Word DOCX dengan tabel yang presisi dan formatting yang baik.</p>
        <button class="btn btn-success" onclick="testWordExport()">Test Word Export</button>
        <div id="word-result" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h3>Direct Links</h3>
        <p>Link langsung untuk download file export:</p>
        <a href="exportServicesJSON.jsp" class="btn btn-warning" target="_blank">Download JSON</a>
        <a href="exportServicesWord.jsp" class="btn btn-warning" target="_blank">Download DOCX</a>
    </div>
    
    <script>
        function testJSONExport() {
            var resultDiv = document.getElementById('json-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>Testing JSON export...</p>';
            
            // Test dengan fetch untuk melihat response
            fetch('exportServicesJSON.jsp')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    }
                    throw new Error('HTTP ' + response.status);
                })
                .then(data => {
                    // Coba parse JSON untuk validasi
                    try {
                        var jsonData = JSON.parse(data);
                        resultDiv.innerHTML = 
                            '<p><strong>✅ JSON Export Success!</strong></p>' +
                            '<p>Content-Type: application/json</p>' +
                            '<p>Total Services: ' + (jsonData.export_info ? jsonData.export_info.total_records : 'N/A') + '</p>' +
                            '<p>Services Array Length: ' + (jsonData.services ? jsonData.services.length : 'N/A') + '</p>' +
                            '<p>JSON is valid and properly formatted.</p>' +
                            '<details><summary>Preview JSON (first 500 chars)</summary><pre>' + 
                            data.substring(0, 500) + '...</pre></details>';
                    } catch (e) {
                        resultDiv.innerHTML = 
                            '<p><strong>❌ JSON Parse Error!</strong></p>' +
                            '<p>Error: ' + e.message + '</p>' +
                            '<details><summary>Raw Response</summary><pre>' + data + '</pre></details>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = 
                        '<p><strong>❌ Request Failed!</strong></p>' +
                        '<p>Error: ' + error.message + '</p>';
                });
        }
        
        function testWordExport() {
            var resultDiv = document.getElementById('word-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>Testing Word export...</p>';
            
            // Test dengan fetch untuk melihat response headers
            fetch('exportServicesWord.jsp')
                .then(response => {
                    var contentType = response.headers.get('content-type');
                    var contentDisposition = response.headers.get('content-disposition');
                    
                    if (response.ok) {
                        return response.blob().then(blob => {
                            resultDiv.innerHTML = 
                                '<p><strong>✅ Word Export Success!</strong></p>' +
                                '<p>Content-Type: ' + contentType + '</p>' +
                                '<p>Content-Disposition: ' + contentDisposition + '</p>' +
                                '<p>File Size: ' + blob.size + ' bytes</p>' +
                                '<p>DOCX file generated successfully.</p>' +
                                '<p><a href="exportServicesWord.jsp" target="_blank" class="btn">Download DOCX File</a></p>';
                        });
                    } else {
                        throw new Error('HTTP ' + response.status);
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = 
                        '<p><strong>❌ Request Failed!</strong></p>' +
                        '<p>Error: ' + error.message + '</p>';
                });
        }
    </script>
</body>
</html>
