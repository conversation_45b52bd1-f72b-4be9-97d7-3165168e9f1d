<%@page contentType="application/json" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>
<%
    // Set response headers for JSON download
    response.setContentType("application/json; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=data_layanan_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json");
    
    try {
        // Query untuk mengambil semua data layanan
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
        ResultSet rs = ps.executeQuery();
        
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0.00");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // Hitung total records
        PreparedStatement countPs = conn.prepareStatement("SELECT COUNT(*) as total FROM services");
        ResultSet countRs = countPs.executeQuery();
        int totalRecords = 0;
        if (countRs.next()) {
            totalRecords = countRs.getInt("total");
        }
        countRs.close();
        countPs.close();
        
        // Build JSON dengan format yang rapi dan sesuai standar
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append("  \"export_info\": {\n");
        json.append("    \"exported_at\": \"").append(dateFormat.format(new Date())).append("\",\n");
        json.append("    \"exported_by\": \"Admin Dashboard\",\n");
        json.append("    \"total_records\": ").append(totalRecords).append("\n");
        json.append("  },\n");
        json.append("  \"services\": [\n");
        
        boolean first = true;
        while (rs.next()) {
            if (!first) {
                json.append(",\n");
            }
            first = false;
            
            int id = rs.getInt("id");
            String name = rs.getString("name");
            String images = rs.getString("images");
            int quantity = rs.getInt("quantity");
            double price = rs.getDouble("price");
            String createdAt = rs.getString("created_at");
            String updatedAt = rs.getString("updated_at");
            
            // Escape JSON strings properly
            name = escapeJsonString(name);
            if (images != null) {
                images = escapeJsonString(images);
            }
            
            json.append("    {\n");
            json.append("      \"id\": ").append(id).append(",\n");
            json.append("      \"name\": \"").append(name).append("\",\n");
            json.append("      \"image\": \"").append(images != null ? images : "").append("\",\n");
            json.append("      \"image_url\": \"").append(images != null ? "dist/img/" + images : "").append("\",\n");
            json.append("      \"quantity\": ").append(quantity).append(",\n");
            json.append("      \"price\": ").append(price).append(",\n");
            json.append("      \"price_formatted\": \"Rp ").append(priceFormat.format(price)).append("\",\n");
            json.append("      \"created_at\": \"").append(createdAt).append("\",\n");
            json.append("      \"updated_at\": \"").append(updatedAt).append("\"\n");
            json.append("    }");
        }
        
        json.append("\n  ]\n");
        json.append("}");
        
        // Output JSON yang sudah diformat
        out.print(json.toString());
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response dengan format JSON yang benar
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        StringBuilder errorJson = new StringBuilder();
        errorJson.append("{\n");
        errorJson.append("  \"error\": true,\n");
        errorJson.append("  \"message\": \"Database error: ").append(escapeJsonString(e.getMessage())).append("\",\n");
        errorJson.append("  \"exported_at\": \"").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("\"\n");
        errorJson.append("}");
        out.print(errorJson.toString());
    } catch (Exception e) {
        // General error response dengan format JSON yang benar
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        StringBuilder errorJson = new StringBuilder();
        errorJson.append("{\n");
        errorJson.append("  \"error\": true,\n");
        errorJson.append("  \"message\": \"Export error: ").append(escapeJsonString(e.getMessage())).append("\",\n");
        errorJson.append("  \"exported_at\": \"").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("\"\n");
        errorJson.append("}");
        out.print(errorJson.toString());
    }
%>

<%!
    // Helper method untuk escape JSON string
    private String escapeJsonString(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t")
                  .replace("\b", "\\b")
                  .replace("\f", "\\f");
    }
%>
