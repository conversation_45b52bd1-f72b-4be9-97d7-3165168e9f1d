<%@page contentType="application/json" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>

<%
    // Set response headers for JSON download
    response.setContentType("application/json");
    response.setHeader("Content-Disposition", "attachment; filename=data_layanan_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json");
    
    try {
        // Query untuk mengambil semua data layanan
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
        ResultSet rs = ps.executeQuery();
        
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0.00");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // Mulai JSON output
        out.print("{");
        out.print("\"export_info\": {");
        out.print("\"exported_at\": \"" + dateFormat.format(new Date()) + "\",");
        out.print("\"exported_by\": \"Admin Dashboard\",");
        out.print("\"total_records\": ");
        
        // Hitung total records
        PreparedStatement countPs = conn.prepareStatement("SELECT COUNT(*) as total FROM services");
        ResultSet countRs = countPs.executeQuery();
        int totalRecords = 0;
        if (countRs.next()) {
            totalRecords = countRs.getInt("total");
        }
        countRs.close();
        countPs.close();
        
        out.print(totalRecords);
        out.print("},");
        out.print("\"services\": [");
        
        boolean first = true;
        while (rs.next()) {
            if (!first) {
                out.print(",");
            }
            first = false;
            
            int id = rs.getInt("id");
            String name = rs.getString("name");
            String images = rs.getString("images");
            int quantity = rs.getInt("quantity");
            double price = rs.getDouble("price");
            String createdAt = rs.getString("created_at");
            String updatedAt = rs.getString("updated_at");
            
            // Escape JSON strings
            name = name.replace("\"", "\\\"").replace("\\", "\\\\").replace("\n", "\\n").replace("\r", "\\r");
            if (images != null) {
                images = images.replace("\"", "\\\"").replace("\\", "\\\\");
            }
            
            out.print("{");
            out.print("\"id\": " + id + ",");
            out.print("\"name\": \"" + name + "\",");
            out.print("\"image\": \"" + (images != null ? images : "") + "\",");
            out.print("\"image_url\": \"" + (images != null ? "dist/img/" + images : "") + "\",");
            out.print("\"quantity\": " + quantity + ",");
            out.print("\"price\": " + price + ",");
            out.print("\"price_formatted\": \"Rp " + priceFormat.format(price) + "\",");
            out.print("\"created_at\": \"" + createdAt + "\",");
            out.print("\"updated_at\": \"" + updatedAt + "\"");
            out.print("}");
        }
        
        out.print("]");
        out.print("}");
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("{");
        out.print("\"error\": true,");
        out.print("\"message\": \"Database error: " + e.getMessage().replace("\"", "\\\"") + "\",");
        out.print("\"exported_at\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\"");
        out.print("}");
    } catch (Exception e) {
        // General error response
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("{");
        out.print("\"error\": true,");
        out.print("\"message\": \"Export error: " + e.getMessage().replace("\"", "\\\"") + "\",");
        out.print("\"exported_at\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\"");
        out.print("}");
    }
%>
