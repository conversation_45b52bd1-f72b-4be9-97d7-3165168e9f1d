<%@page contentType="application/pdf" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>
<%@page import="java.io.*"%>
<%
    // Set response headers for PDF download
    response.setContentType("application/pdf");
    response.setHeader("Content-Disposition", "attachment; filename=Data_Transaksi_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".pdf");
    
    try {
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        
        // Query untuk mengambil semua data transaksi dengan join
        PreparedStatement ps = conn.prepareStatement(
            "SELECT t.*, u.name as user_name, s.name as service_name " +
            "FROM transaction t " +
            "LEFT JOIN user u ON t.user_id = u.id " +
            "LEFT JOIN services s ON t.service_id = s.id " +
            "ORDER BY t.id ASC"
        );
        ResultSet rs = ps.executeQuery();
        
        // Hitung total records dan statistik
        PreparedStatement summaryPs = conn.prepareStatement(
            "SELECT COUNT(*) as total_transactions, " +
            "SUM(CASE WHEN status = 'completed' THEN total ELSE 0 END) as total_completed_value, " +
            "SUM(CASE WHEN status = 'pending' THEN total ELSE 0 END) as total_pending_value, " +
            "SUM(CASE WHEN status = 'cancelled' THEN total ELSE 0 END) as total_cancelled_value, " +
            "COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count, " +
            "COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count, " +
            "COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count " +
            "FROM transaction"
        );
        ResultSet summaryRs = summaryPs.executeQuery();
        
        int totalTransactions = 0;
        double totalCompletedValue = 0;
        double totalPendingValue = 0;
        double totalCancelledValue = 0;
        int completedCount = 0;
        int pendingCount = 0;
        int cancelledCount = 0;
        
        if (summaryRs.next()) {
            totalTransactions = summaryRs.getInt("total_transactions");
            totalCompletedValue = summaryRs.getDouble("total_completed_value");
            totalPendingValue = summaryRs.getDouble("total_pending_value");
            totalCancelledValue = summaryRs.getDouble("total_cancelled_value");
            completedCount = summaryRs.getInt("completed_count");
            pendingCount = summaryRs.getInt("pending_count");
            cancelledCount = summaryRs.getInt("cancelled_count");
        }
        summaryRs.close();
        summaryPs.close();
        
        // Create PDF content using simple PDF format
        StringBuilder pdfContent = new StringBuilder();
        
        // PDF Header
        pdfContent.append("%PDF-1.4\n");
        pdfContent.append("1 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Type /Catalog\n");
        pdfContent.append("/Pages 2 0 R\n");
        pdfContent.append(">>\n");
        pdfContent.append("endobj\n\n");
        
        // Pages object
        pdfContent.append("2 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Type /Pages\n");
        pdfContent.append("/Kids [3 0 R]\n");
        pdfContent.append("/Count 1\n");
        pdfContent.append(">>\n");
        pdfContent.append("endobj\n\n");
        
        // Page object
        pdfContent.append("3 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Type /Page\n");
        pdfContent.append("/Parent 2 0 R\n");
        pdfContent.append("/MediaBox [0 0 842 595]\n"); // A4 landscape
        pdfContent.append("/Contents 4 0 R\n");
        pdfContent.append("/Resources <<\n");
        pdfContent.append("/Font <<\n");
        pdfContent.append("/F1 5 0 R\n");
        pdfContent.append("/F2 6 0 R\n");
        pdfContent.append(">>\n");
        pdfContent.append(">>\n");
        pdfContent.append(">>\n");
        pdfContent.append("endobj\n\n");
        
        // Content stream
        StringBuilder contentStream = new StringBuilder();
        contentStream.append("BT\n");
        
        // Title
        contentStream.append("/F2 16 Tf\n");
        contentStream.append("421 550 Td\n"); // Center position
        contentStream.append("(DATA TRANSAKSI ARQETA) Tj\n");
        
        // Export date
        contentStream.append("/F1 10 Tf\n");
        contentStream.append("-200 -20 Td\n");
        contentStream.append("(Diekspor pada: ").append(dateFormat.format(new Date())).append(") Tj\n");
        
        // Summary section
        contentStream.append("/F2 12 Tf\n");
        contentStream.append("-200 -30 Td\n");
        contentStream.append("(RINGKASAN TRANSAKSI) Tj\n");
        
        contentStream.append("/F1 10 Tf\n");
        contentStream.append("0 -15 Td\n");
        contentStream.append("(Total Transaksi: ").append(totalTransactions).append(") Tj\n");
        
        contentStream.append("0 -12 Td\n");
        contentStream.append("(Transaksi Selesai: ").append(completedCount).append(" \\(Rp ").append(priceFormat.format(totalCompletedValue)).append("\\)) Tj\n");
        
        contentStream.append("0 -12 Td\n");
        contentStream.append("(Transaksi Menunggu: ").append(pendingCount).append(" \\(Rp ").append(priceFormat.format(totalPendingValue)).append("\\)) Tj\n");
        
        contentStream.append("0 -12 Td\n");
        contentStream.append("(Transaksi Dibatalkan: ").append(cancelledCount).append(" \\(Rp ").append(priceFormat.format(totalCancelledValue)).append("\\)) Tj\n");
        
        // Table header
        contentStream.append("/F2 9 Tf\n");
        contentStream.append("-200 -30 Td\n");
        
        // Table headers with proper spacing
        String[] headers = {"ID", "Nama Layanan", "Pengguna", "Jumlah", "Unit", "Total", "Status", "Tanggal"};
        int[] columnWidths = {30, 120, 100, 80, 40, 80, 70, 90}; // Column widths
        int startX = 50;
        
        // Draw table header
        for (int i = 0; i < headers.length; i++) {
            contentStream.append(startX + (i == 0 ? 0 : columnWidths[i-1]) + " 0 Td\n");
            contentStream.append("(").append(headers[i]).append(") Tj\n");
            if (i < headers.length - 1) {
                contentStream.append("-").append(startX + (i == 0 ? 0 : columnWidths[i-1])).append(" 0 Td\n");
            }
        }
        
        // Table data
        contentStream.append("/F1 8 Tf\n");
        int yPosition = -15;
        
        while (rs.next()) {
            int id = rs.getInt("id");
            String serviceName = rs.getString("service_name");
            String userName = rs.getString("user_name");
            double amount = rs.getDouble("amount");
            int unit = rs.getInt("unit");
            double total = rs.getDouble("total");
            String status = rs.getString("status");
            String createdAt = rs.getString("created_at");
            
            // Parse and format date
            String formattedDate = createdAt;
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(createdAt);
                formattedDate = dateFormat.format(date);
            } catch (Exception e) {
                // Keep original format if parsing fails
            }
            
            // Format status
            String statusText = "";
            switch(status) {
                case "pending":
                    statusText = "Menunggu";
                    break;
                case "completed":
                    statusText = "Selesai";
                    break;
                case "cancelled":
                    statusText = "Dibatalkan";
                    break;
                default:
                    statusText = status;
            }
            
            // Truncate long text to fit columns
            serviceName = serviceName != null ? (serviceName.length() > 15 ? serviceName.substring(0, 15) + "..." : serviceName) : "N/A";
            userName = userName != null ? (userName.length() > 12 ? userName.substring(0, 12) + "..." : userName) : "N/A";
            
            contentStream.append("-").append(startX * headers.length).append(" ").append(yPosition).append(" Td\n");
            
            // Row data
            String[] cellData = {
                String.valueOf(id),
                serviceName,
                userName,
                "Rp " + priceFormat.format(amount),
                String.valueOf(unit),
                "Rp " + priceFormat.format(total),
                statusText,
                formattedDate
            };
            
            for (int i = 0; i < cellData.length; i++) {
                contentStream.append(startX + (i == 0 ? 0 : columnWidths[i-1]) + " 0 Td\n");
                contentStream.append("(").append(escapePdfString(cellData[i])).append(") Tj\n");
                if (i < cellData.length - 1) {
                    contentStream.append("-").append(startX + (i == 0 ? 0 : columnWidths[i-1])).append(" 0 Td\n");
                }
            }
            
            yPosition = -12;
        }
        
        contentStream.append("ET\n");
        
        // Content object
        String contentStreamStr = contentStream.toString();
        pdfContent.append("4 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Length ").append(contentStreamStr.length()).append("\n");
        pdfContent.append(">>\n");
        pdfContent.append("stream\n");
        pdfContent.append(contentStreamStr);
        pdfContent.append("endstream\n");
        pdfContent.append("endobj\n\n");
        
        // Font objects
        pdfContent.append("5 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Type /Font\n");
        pdfContent.append("/Subtype /Type1\n");
        pdfContent.append("/BaseFont /Helvetica\n");
        pdfContent.append(">>\n");
        pdfContent.append("endobj\n\n");
        
        pdfContent.append("6 0 obj\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Type /Font\n");
        pdfContent.append("/Subtype /Type1\n");
        pdfContent.append("/BaseFont /Helvetica-Bold\n");
        pdfContent.append(">>\n");
        pdfContent.append("endobj\n\n");
        
        // Cross-reference table
        pdfContent.append("xref\n");
        pdfContent.append("0 7\n");
        pdfContent.append("0000000000 65535 f \n");
        pdfContent.append("0000000009 00000 n \n");
        pdfContent.append("0000000074 00000 n \n");
        pdfContent.append("0000000131 00000 n \n");
        pdfContent.append("0000000290 00000 n \n");
        pdfContent.append(String.format("0000%06d 00000 n \n", pdfContent.length() + 50));
        pdfContent.append(String.format("0000%06d 00000 n \n", pdfContent.length() + 120));
        
        // Trailer
        pdfContent.append("trailer\n");
        pdfContent.append("<<\n");
        pdfContent.append("/Size 7\n");
        pdfContent.append("/Root 1 0 R\n");
        pdfContent.append(">>\n");
        pdfContent.append("startxref\n");
        pdfContent.append(pdfContent.length() - 200).append("\n");
        pdfContent.append("%%EOF\n");
        
        // Write PDF to response
        byte[] pdfBytes = pdfContent.toString().getBytes("UTF-8");
        response.getOutputStream().write(pdfBytes);
        response.getOutputStream().flush();
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Database Error: " + e.getMessage());
    } catch (Exception e) {
        // General error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Export Error: " + e.getMessage());
    }
%>

<%!
    // Helper method untuk escape PDF string characters
    private String escapePdfString(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("(", "\\(")
                  .replace(")", "\\)")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
%>
