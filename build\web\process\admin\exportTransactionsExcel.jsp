<%@page contentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.text.DecimalFormat"%>
<%@page import="java.io.*"%>
<%@page import="java.util.zip.*"%>
<%@page import="java.util.Base64"%>
<%
    // Set response headers for Excel download
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment; filename=Data_Transaksi_" + 
        new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".xlsx");
    
    try {
        // Format untuk harga
        DecimalFormat priceFormat = new DecimalFormat("#,##0");
        
        // Format untuk tanggal
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        
        // Query untuk mengambil semua data transaksi dengan join
        PreparedStatement ps = conn.prepareStatement(
            "SELECT t.*, u.name as user_name, s.name as service_name " +
            "FROM transaction t " +
            "LEFT JOIN user u ON t.user_id = u.id " +
            "LEFT JOIN services s ON t.service_id = s.id " +
            "ORDER BY t.id ASC"
        );
        ResultSet rs = ps.executeQuery();
        
        // Hitung total records dan statistik
        PreparedStatement summaryPs = conn.prepareStatement(
            "SELECT COUNT(*) as total_transactions, " +
            "SUM(CASE WHEN status = 'completed' THEN total ELSE 0 END) as total_completed_value, " +
            "SUM(CASE WHEN status = 'pending' THEN total ELSE 0 END) as total_pending_value, " +
            "SUM(CASE WHEN status = 'cancelled' THEN total ELSE 0 END) as total_cancelled_value, " +
            "COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count, " +
            "COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count, " +
            "COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count " +
            "FROM transaction"
        );
        ResultSet summaryRs = summaryPs.executeQuery();
        
        int totalTransactions = 0;
        double totalCompletedValue = 0;
        double totalPendingValue = 0;
        double totalCancelledValue = 0;
        int completedCount = 0;
        int pendingCount = 0;
        int cancelledCount = 0;
        
        if (summaryRs.next()) {
            totalTransactions = summaryRs.getInt("total_transactions");
            totalCompletedValue = summaryRs.getDouble("total_completed_value");
            totalPendingValue = summaryRs.getDouble("total_pending_value");
            totalCancelledValue = summaryRs.getDouble("total_cancelled_value");
            completedCount = summaryRs.getInt("completed_count");
            pendingCount = summaryRs.getInt("pending_count");
            cancelledCount = summaryRs.getInt("cancelled_count");
        }
        summaryRs.close();
        summaryPs.close();
        
        // Create Excel content using Office Open XML format
        ByteArrayOutputStream excelOutput = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(excelOutput);
        
        // Add [Content_Types].xml
        zipOut.putNextEntry(new ZipEntry("[Content_Types].xml"));
        String contentTypes = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Types xmlns=\"http://schemas.openxmlformats.org/package/2006/content-types\">" +
            "<Default Extension=\"rels\" ContentType=\"application/vnd.openxmlformats-package.relationships+xml\"/>" +
            "<Default Extension=\"xml\" ContentType=\"application/xml\"/>" +
            "<Override PartName=\"/xl/workbook.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml\"/>" +
            "<Override PartName=\"/xl/worksheets/sheet1.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml\"/>" +
            "<Override PartName=\"/xl/sharedStrings.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml\"/>" +
            "<Override PartName=\"/xl/styles.xml\" ContentType=\"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml\"/>" +
            "</Types>";
        zipOut.write(contentTypes.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add _rels/.rels
        zipOut.putNextEntry(new ZipEntry("_rels/.rels"));
        String rels = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">" +
            "<Relationship Id=\"rId1\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\" Target=\"xl/workbook.xml\"/>" +
            "</Relationships>";
        zipOut.write(rels.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add xl/_rels/workbook.xml.rels
        zipOut.putNextEntry(new ZipEntry("xl/_rels/workbook.xml.rels"));
        String workbookRels = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">" +
            "<Relationship Id=\"rId1\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet\" Target=\"worksheets/sheet1.xml\"/>" +
            "<Relationship Id=\"rId2\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings\" Target=\"sharedStrings.xml\"/>" +
            "<Relationship Id=\"rId3\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\" Target=\"styles.xml\"/>" +
            "</Relationships>";
        zipOut.write(workbookRels.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add xl/workbook.xml
        zipOut.putNextEntry(new ZipEntry("xl/workbook.xml"));
        String workbook = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<workbook xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\">" +
            "<sheets>" +
            "<sheet name=\"Data Transaksi\" sheetId=\"1\" r:id=\"rId1\"/>" +
            "</sheets>" +
            "</workbook>";
        zipOut.write(workbook.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add xl/sharedStrings.xml
        zipOut.putNextEntry(new ZipEntry("xl/sharedStrings.xml"));
        String sharedStrings = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<sst xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\" count=\"0\" uniqueCount=\"0\"/>";
        zipOut.write(sharedStrings.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Add xl/styles.xml
        zipOut.putNextEntry(new ZipEntry("xl/styles.xml"));
        String styles = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<styleSheet xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\">" +
            "<fonts count=\"2\">" +
            "<font><sz val=\"11\"/><name val=\"Calibri\"/></font>" +
            "<font><b/><sz val=\"11\"/><name val=\"Calibri\"/></font>" +
            "</fonts>" +
            "<fills count=\"2\">" +
            "<fill><patternFill patternType=\"none\"/></fill>" +
            "<fill><patternFill patternType=\"gray125\"/></fill>" +
            "</fills>" +
            "<borders count=\"2\">" +
            "<border><left/><right/><top/><bottom/><diagonal/></border>" +
            "<border><left style=\"thin\"/><right style=\"thin\"/><top style=\"thin\"/><bottom style=\"thin\"/><diagonal/></border>" +
            "</borders>" +
            "<cellXfs count=\"3\">" +
            "<xf numFmtId=\"0\" fontId=\"0\" fillId=\"0\" borderId=\"0\"/>" +
            "<xf numFmtId=\"0\" fontId=\"1\" fillId=\"0\" borderId=\"1\" applyFont=\"1\" applyBorder=\"1\"/>" +
            "<xf numFmtId=\"0\" fontId=\"0\" fillId=\"0\" borderId=\"1\" applyBorder=\"1\"/>" +
            "</cellXfs>" +
            "</styleSheet>";
        zipOut.write(styles.getBytes("UTF-8"));
        zipOut.closeEntry();
        
        // Create worksheet content
        StringBuilder worksheet = new StringBuilder();
        worksheet.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
        worksheet.append("<worksheet xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\">");
        worksheet.append("<sheetData>");
        
        // Title row
        worksheet.append("<row r=\"1\">");
        worksheet.append("<c r=\"A1\" s=\"1\" t=\"inlineStr\"><is><t>DATA TRANSAKSI ARQETA</t></is></c>");
        worksheet.append("</row>");
        
        // Export date row
        worksheet.append("<row r=\"2\">");
        worksheet.append("<c r=\"A2\" t=\"inlineStr\"><is><t>Diekspor pada: ").append(escapeXml(dateFormat.format(new Date()))).append("</t></is></c>");
        worksheet.append("</row>");
        
        // Empty row
        worksheet.append("<row r=\"3\"></row>");
        
        // Summary rows
        worksheet.append("<row r=\"4\">");
        worksheet.append("<c r=\"A4\" s=\"1\" t=\"inlineStr\"><is><t>RINGKASAN TRANSAKSI</t></is></c>");
        worksheet.append("</row>");
        
        worksheet.append("<row r=\"5\">");
        worksheet.append("<c r=\"A5\" t=\"inlineStr\"><is><t>Total Transaksi: ").append(totalTransactions).append("</t></is></c>");
        worksheet.append("</row>");
        
        worksheet.append("<row r=\"6\">");
        worksheet.append("<c r=\"A6\" t=\"inlineStr\"><is><t>Transaksi Selesai: ").append(completedCount).append(" (Rp ").append(priceFormat.format(totalCompletedValue)).append(")</t></is></c>");
        worksheet.append("</row>");
        
        worksheet.append("<row r=\"7\">");
        worksheet.append("<c r=\"A7\" t=\"inlineStr\"><is><t>Transaksi Menunggu: ").append(pendingCount).append(" (Rp ").append(priceFormat.format(totalPendingValue)).append(")</t></is></c>");
        worksheet.append("</row>");
        
        worksheet.append("<row r=\"8\">");
        worksheet.append("<c r=\"A8\" t=\"inlineStr\"><is><t>Transaksi Dibatalkan: ").append(cancelledCount).append(" (Rp ").append(priceFormat.format(totalCancelledValue)).append(")</t></is></c>");
        worksheet.append("</row>");
        
        // Empty row
        worksheet.append("<row r=\"9\"></row>");
        
        // Header row
        worksheet.append("<row r=\"10\">");
        String[] headers = {"ID", "Nama Layanan", "Pengguna", "Jumlah", "Unit", "Total", "Status", "Tanggal"};
        char[] columns = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'};
        for (int i = 0; i < headers.length; i++) {
            worksheet.append("<c r=\"").append(columns[i]).append("10\" s=\"1\" t=\"inlineStr\"><is><t>").append(escapeXml(headers[i])).append("</t></is></c>");
        }
        worksheet.append("</row>");
        
        // Data rows
        int rowNumber = 11;
        while (rs.next()) {
            int id = rs.getInt("id");
            String serviceName = rs.getString("service_name");
            String userName = rs.getString("user_name");
            double amount = rs.getDouble("amount");
            int unit = rs.getInt("unit");
            double total = rs.getDouble("total");
            String status = rs.getString("status");
            String createdAt = rs.getString("created_at");
            
            // Parse and format date
            String formattedDate = createdAt;
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(createdAt);
                formattedDate = dateFormat.format(date);
            } catch (Exception e) {
                // Keep original format if parsing fails
            }
            
            // Format status
            String statusText = "";
            switch(status) {
                case "pending":
                    statusText = "Menunggu";
                    break;
                case "completed":
                    statusText = "Selesai";
                    break;
                case "cancelled":
                    statusText = "Dibatalkan";
                    break;
                default:
                    statusText = status;
            }
            
            worksheet.append("<row r=\"").append(rowNumber).append("\">");
            
            // Cell data
            String[] cellData = {
                String.valueOf(id),
                serviceName != null ? serviceName : "Layanan Tidak Ditemukan",
                userName != null ? userName : "User Tidak Ditemukan",
                "Rp " + priceFormat.format(amount),
                String.valueOf(unit),
                "Rp " + priceFormat.format(total),
                statusText,
                formattedDate
            };
            
            for (int i = 0; i < cellData.length; i++) {
                worksheet.append("<c r=\"").append(columns[i]).append(rowNumber).append("\" s=\"2\" t=\"inlineStr\"><is><t>").append(escapeXml(cellData[i])).append("</t></is></c>");
            }
            
            worksheet.append("</row>");
            rowNumber++;
        }
        
        worksheet.append("</sheetData>");
        worksheet.append("</worksheet>");
        
        // Add xl/worksheets/sheet1.xml
        zipOut.putNextEntry(new ZipEntry("xl/worksheets/sheet1.xml"));
        zipOut.write(worksheet.toString().getBytes("UTF-8"));
        zipOut.closeEntry();
        
        zipOut.close();
        
        // Write Excel to response
        byte[] excelBytes = excelOutput.toByteArray();
        response.getOutputStream().write(excelBytes);
        response.getOutputStream().flush();
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        // Error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Database Error: " + e.getMessage());
    } catch (Exception e) {
        // General error response
        response.setContentType("text/plain");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.clear();
        out.print("Export Error: " + e.getMessage());
    }
%>

<%!
    // Helper method untuk escape XML characters
    private String escapeXml(String str) {
        if (str == null) return "";
        return str.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&apos;");
    }
%>
