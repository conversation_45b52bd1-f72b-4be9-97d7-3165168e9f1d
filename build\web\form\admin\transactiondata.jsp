<%--
  Document    : transactiondata
  Created on  : Jul 30, 2025
  Author      : Arqeta
  Description : <PERSON><PERSON> admin untuk mengelola data transaksi.
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*, java.util.*" %>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Transaksi</h2>
        <div class="action-bar">
            <div class="export-buttons">
                <button class="btn-export btn-export-excel" onclick="exportToExcel()" title="Ekspor ke Excel">
                    <i data-feather="file-text"></i> Ekspor Excel
                </button>
                <button class="btn-export btn-export-pdf" onclick="exportToPDF()" title="Ekspor ke PDF">
                    <i data-feather="file"></i> Ekspor PDF
                </button>
            </div>
            <div class="status-filter">
                <select id="statusFilter" onchange="filterTransactions()">
                    <option value="">Semua Status</option>
                    <option value="pending">Menunggu</option>
                    <option value="completed">Selesai</option>
                    <option value="cancelled">Dibatalkan</option>
                </select>
            </div>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama Layanan</th>
                        <th>Pengguna</th>
                        <th>Jumlah</th>
                        <th>Unit</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="transactionTableBody">
                    <%
                        PreparedStatement ps = null;
                        ResultSet rs = null;
                        try {
                            // Query untuk mengambil data transaksi dengan join ke tabel user dan services
                            String query = "SELECT t.*, u.name as user_name, s.name as service_name "
                                         + "FROM transaction t "
                                         + "LEFT JOIN user u ON t.user_id = u.id "
                                         + "LEFT JOIN services s ON t.service_id = s.id "
                                         + "ORDER BY t.id ASC";

                            ps = conn.prepareStatement(query);
                            rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String serviceName = rs.getString("service_name");
                                String userName = rs.getString("user_name");
                                double amount = rs.getDouble("amount");
                                int unit = rs.getInt("unit");
                                double total = rs.getDouble("total");
                                String status = rs.getString("status");
                                String createdAt = rs.getString("created_at");

                                String statusClass = "";
                                String statusText = "";

                                switch (status.toLowerCase()) {
                                    case "menunggu":
                                    case "pending":
                                        statusClass = "status-pending";
                                        statusText = "Menunggu";
                                        break;
                                    case "diterima":
                                    case "completed":
                                        statusClass = "status-approved";
                                        statusText = "Diterima";
                                        break;
                                    case "ditolak":
                                    case "cancelled":
                                        statusClass = "status-rejected";
                                        statusText = "Ditolak";
                                        break;
                                    default:
                                        statusClass = "status-pending";
                                        statusText = status;
                                }
                    %>
                    <tr data-status="<%= status %>">
                        <td><%= id %></td>
                        <td><%= (serviceName != null) ? serviceName : "Layanan Tidak Ditemukan" %></td>
                        <td><%= (userName != null) ? userName : "User Tidak Ditemukan" %></td>
                        <td>Rp <%= String.format("%,.0f", amount) %></td>
                        <td><%= unit %></td>
                        <td>Rp <%= String.format("%,.0f", total) %></td>
                        <td>
                            <span class="status-badge <%= statusClass %>">
                                <%= statusText %>
                            </span>
                        </td>
                        <td><%= createdAt %></td>
                        <td>
                            <div class="action-buttons">
                                <% if ("pending".equalsIgnoreCase(status) || "menunggu".equalsIgnoreCase(status)) { %>
                                    <button class="btn-edit" onclick="manageTransaction(<%= id %>, 'accept')" title="Terima">
                                        <i data-feather="check"></i>
                                    </button>
                                    <button class="btn-delete" onclick="manageTransaction(<%= id %>, 'reject')" title="Tolak">
                                        <i data-feather="x"></i>
                                    </button>
                                <% } else { %>
                                    <span class="status-info">Transaksi sudah diproses</span>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='9' class='text-center text-danger'>Error: " + e.getMessage() + "</td></tr>");
                            e.printStackTrace();
                        } finally {
                            if (rs != null) try { rs.close(); } catch (SQLException e) { e.printStackTrace(); }
                            if (ps != null) try { ps.close(); } catch (SQLException e) { e.printStackTrace(); }
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    /**
     * Mengelola aksi transaksi seperti menerima atau menolak.
     * @param {number} id - ID transaksi.
     * @param {string} action - Aksi yang akan dilakukan ('accept' atau 'reject').
     */
    function manageTransaction(id, action) {
        let confirmMessage = "Apakah Anda yakin ingin melakukan tindakan ini?";
        if (action === "accept") {
            confirmMessage = "Apakah Anda yakin ingin menerima transaksi ini?";
        } else if (action === "reject") {
            confirmMessage = "Apakah Anda yakin ingin menolak transaksi ini?";
        }

        if (confirm(confirmMessage)) {
            const form = document.createElement("form");
            form.method = "POST";
            form.action = "<%= request.getContextPath() %>/process/admin/manageTransaction.jsp";

            const idInput = document.createElement("input");
            idInput.type = "hidden";
            idInput.name = "id";
            idInput.value = id;

            const actionInput = document.createElement("input");
            actionInput.type = "hidden";
            actionInput.name = "action";
            actionInput.value = action;

            form.appendChild(idInput);
            form.appendChild(actionInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    /**
     * Menyaring baris tabel transaksi berdasarkan status yang dipilih.
     */
    function filterTransactions() {
        const filter = document.getElementById("statusFilter").value.toLowerCase();
        const rows = document.querySelectorAll("#transactionTableBody tr");

        rows.forEach((row) => {
            const status = row.getAttribute("data-status").toLowerCase();
            if (filter === "" || status === filter) {
                row.style.display = "";
            } else {
                row.style.display = "none";
            }
        });
    }

    /**
     * Menampilkan notifikasi kepada user.
     * @param {string} message - Pesan notifikasi.
     * @param {string} [type='info'] - Tipe notifikasi ('info', 'success', 'error').
     */
    function showNotification(message, type = "info") {
        const notification = document.createElement("div");
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `<span>${message}</span>`;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add("show");
        }, 100);

        setTimeout(() => {
            notification.classList.remove("show");
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Fungsi untuk memicu unduhan file dan menampilkan feedback UI.
     * @param {string} url - URL untuk mengunduh file.
     * @param {string} fileName - Nama file yang akan diunduh.
     * @param {HTMLElement} buttonElement - Elemen tombol yang diklik.
     * @param {string} fileType - Tipe file (e.g., 'Excel', 'PDF').
     */
    function exportFile(url, fileName, buttonElement, fileType) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = '<i data-feather="loader"></i> Mengekspor...';
        buttonElement.disabled = true;
        feather.replace();

        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;

        link.onclick = () => {
            setTimeout(() => {
                buttonElement.innerHTML = originalText;
                buttonElement.disabled = false;
                feather.replace();
                showNotification(`File ${fileType} berhasil diunduh!`, "success");
            }, 2000);
        };

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Fungsi ekspor ke Excel.
     */
    function exportToExcel() {
        const url = "<%= request.getContextPath() %>/process/admin/exportTransactionsExcel.jsp";
        const fileName = `data_transaksi_${new Date().toISOString().slice(0, 10)}.xlsx`;
        const btn = document.querySelector(".btn-export-excel");
        exportFile(url, fileName, btn, "Excel");
    }

    /**
     * Fungsi ekspor ke PDF.
     */
    function exportToPDF() {
        const url = "<%= request.getContextPath() %>/process/admin/exportTransactionsPDF.jsp";
        const fileName = `data_transaksi_${new Date().toISOString().slice(0, 10)}.pdf`;
        const btn = document.querySelector(".btn-export-pdf");
        exportFile(url, fileName, btn, "PDF");
    }
</script>
