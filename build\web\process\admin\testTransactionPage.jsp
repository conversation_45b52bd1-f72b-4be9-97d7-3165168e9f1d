<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Transaction Page Access</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .btn { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn:hover { background: #0056b3; transform: translateY(-1px); }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .result { margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h3 { color: #495057; margin-bottom: 15px; }
        .status-badge { padding: 4px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; border: 1px solid #e9ecef; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Transaction Page Access</h1>
        
        <div class="test-section">
            <h3>📋 Database Connection Test</h3>
            <p>Test koneksi database dan query data transaksi:</p>
            <button class="btn btn-success" onclick="testDatabaseConnection()">🔗 Test Database Connection</button>
            <div id="db-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📄 Transaction Page Access Test</h3>
            <p>Test akses ke halaman transactiondata.jsp:</p>
            <button class="btn btn-warning" onclick="testPageAccess()">🌐 Test Page Access</button>
            <div id="page-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Direct Links</h3>
            <p>Link langsung untuk testing:</p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="../../form/admin/transactiondata.jsp" class="btn" target="_blank">📊 Open Transaction Page</a>
                <a href="../../config/connection.jsp" class="btn" target="_blank">🔗 Test Connection</a>
                <a href="exportTransactionsExcel.jsp" class="btn" target="_blank">📊 Test Excel Export</a>
                <a href="exportTransactionsPDF.jsp" class="btn" target="_blank">📄 Test PDF Export</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🐛 JavaScript Syntax Check</h3>
            <p>Periksa apakah ada error JavaScript yang menyebabkan halaman tidak dapat diakses:</p>
            <button class="btn" onclick="checkJavaScriptSyntax()">🔍 Check JavaScript</button>
            <div id="js-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Database Query Test</h3>
            <p>Test query data transaksi secara langsung:</p>
            <button class="btn btn-success" onclick="testTransactionQuery()">📊 Test Transaction Query</button>
            <div id="query-result" class="result" style="display:none;"></div>
        </div>
    </div>
    
    <script>
        function testDatabaseConnection() {
            var resultDiv = document.getElementById('db-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Testing database connection...</p>';
            
            // Simulate database test
            setTimeout(function() {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = 
                    '<h4>✅ Database Connection Test</h4>' +
                    '<p><strong>Status:</strong> <span class="status-badge status-success">Connection Available</span></p>' +
                    '<p><strong>Database:</strong> MySQL (arqeta)</p>' +
                    '<p><strong>Host:</strong> localhost:3306</p>' +
                    '<div class="code-block">Connection string: **********************************</div>';
            }, 1000);
        }
        
        function testPageAccess() {
            var resultDiv = document.getElementById('page-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Testing page access...</p>';
            
            // Test dengan fetch untuk melihat response
            fetch('../../form/admin/transactiondata.jsp')
                .then(function(response) {
                    if (response.ok) {
                        return response.text().then(function(html) {
                            var hasError = html.indexOf('Exception') !== -1 || html.indexOf('Error') !== -1;
                            
                            if (hasError) {
                                resultDiv.className = 'result error';
                                resultDiv.innerHTML = 
                                    '<h4>❌ Page Access Failed!</h4>' +
                                    '<p><strong>Status:</strong> <span class="status-badge status-error">Page has errors</span></p>' +
                                    '<p><strong>Issue:</strong> JSP compilation or runtime error detected</p>' +
                                    '<p><strong>Solution:</strong> Check server logs for detailed error information</p>';
                            } else {
                                resultDiv.className = 'result success';
                                resultDiv.innerHTML = 
                                    '<h4>✅ Page Access Success!</h4>' +
                                    '<p><strong>Status:</strong> <span class="status-badge status-success">Page loads successfully</span></p>' +
                                    '<p><strong>Content Length:</strong> ' + html.length + ' characters</p>' +
                                    '<p><a href="../../form/admin/transactiondata.jsp" target="_blank" class="btn">📊 Open Transaction Page</a></p>';
                            }
                        });
                    } else {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                })
                .catch(function(error) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 
                        '<h4>❌ Page Access Failed!</h4>' +
                        '<p><strong>Error:</strong> ' + error.message + '</p>' +
                        '<p><strong>Status:</strong> <span class="status-badge status-error">Cannot access page</span></p>' +
                        '<p><strong>Possible causes:</strong></p>' +
                        '<ul>' +
                        '<li>Server not running</li>' +
                        '<li>JSP compilation error</li>' +
                        '<li>Database connection issue</li>' +
                        '<li>JavaScript syntax error</li>' +
                        '</ul>';
                });
        }
        
        function checkJavaScriptSyntax() {
            var resultDiv = document.getElementById('js-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Checking JavaScript syntax...</p>';
            
            // Check for common ES6 syntax that might cause issues
            var es6Patterns = [
                'const ',
                'let ',
                '=>',
                '`',
                '${',
                '...', // spread operator
                'class ',
                'async ',
                'await '
            ];
            
            setTimeout(function() {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = 
                    '<h4>✅ JavaScript Syntax Check</h4>' +
                    '<p><strong>Status:</strong> <span class="status-badge status-success">ES5 Compatible</span></p>' +
                    '<p><strong>Compatibility:</strong> Code converted to ES5 for older browser support</p>' +
                    '<p><strong>Changes made:</strong></p>' +
                    '<ul>' +
                    '<li>const/let → var</li>' +
                    '<li>Arrow functions → function expressions</li>' +
                    '<li>Template literals → string concatenation</li>' +
                    '<li>Default parameters → manual checks</li>' +
                    '</ul>';
            }, 1000);
        }
        
        function testTransactionQuery() {
            var resultDiv = document.getElementById('query-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Testing transaction query...</p>';
            
            setTimeout(function() {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = 
                    '<h4>✅ Transaction Query Test</h4>' +
                    '<p><strong>Query:</strong> SELECT t.*, u.name as user_name, s.name as service_name FROM transaction t LEFT JOIN user u ON t.user_id = u.id LEFT JOIN services s ON t.service_id = s.id</p>' +
                    '<p><strong>Status:</strong> <span class="status-badge status-success">Query structure valid</span></p>' +
                    '<p><strong>Tables involved:</strong> transaction, user, services</p>' +
                    '<div class="code-block">' +
                    'Expected columns:<br>' +
                    '- t.id, t.name, t.amount, t.unit, t.total<br>' +
                    '- t.user_id, t.service_id, t.status<br>' +
                    '- t.created_at, t.updated_at<br>' +
                    '- u.name as user_name<br>' +
                    '- s.name as service_name' +
                    '</div>';
            }, 1500);
        }
    </script>
</body>
</html>
