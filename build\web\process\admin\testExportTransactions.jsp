<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Export Functions - Transaction Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .btn { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn:hover { background: #0056b3; transform: translateY(-1px); }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-info { background: #17a2b8; }
        .btn-info:hover { background: #138496; }
        .result { margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .feature-list li:last-child { border-bottom: none; }
        .status-badge { padding: 4px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-success { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h3 { color: #495057; margin-bottom: 15px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Export Functions - Data Transaksi</h1>
        
        <div class="test-section">
            <h3>📊 Excel Export Test</h3>
            <p>Test ekspor data transaksi ke format Excel (.xlsx) dengan format yang rapi dan data lengkap.</p>
            <p><strong>Fitur Excel Export:</strong></p>
            <ul class="feature-list">
                <li>✅ Format XLSX asli dengan Office Open XML</li>
                <li>✅ Ringkasan statistik transaksi (total, selesai, menunggu, dibatalkan)</li>
                <li>✅ Data lengkap dengan join ke tabel user dan services</li>
                <li>✅ Formatting harga dengan pemisah ribuan</li>
                <li>✅ Styling dengan header bold dan border</li>
                <li>✅ Filename dengan timestamp</li>
            </ul>
            <button class="btn btn-success" onclick="testExcelExport()">🧪 Test Excel Export</button>
            <div id="excel-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📄 PDF Export Test</h3>
            <p>Test ekspor data transaksi ke format PDF dengan tabel yang terpusat, tidak terpotong, dan ukuran yang tepat.</p>
            <p><strong>Fitur PDF Export:</strong></p>
            <ul class="feature-list">
                <li>✅ Format PDF asli dengan layout landscape A4</li>
                <li>✅ Tabel terpusat dengan kolom yang proporsional</li>
                <li>✅ Text tidak terpotong dengan truncation untuk kolom panjang</li>
                <li>✅ Ringkasan statistik di bagian atas</li>
                <li>✅ Font Helvetica dengan ukuran yang readable</li>
                <li>✅ Header dengan font bold dan data dengan font regular</li>
            </ul>
            <button class="btn btn-info" onclick="testPDFExport()">🧪 Test PDF Export</button>
            <div id="pdf-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Direct Download Links</h3>
            <p>Link langsung untuk download file export:</p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="exportTransactionsExcel.jsp" class="btn btn-warning" target="_blank">📊 Download Excel</a>
                <a href="exportTransactionsPDF.jsp" class="btn btn-warning" target="_blank">📄 Download PDF</a>
                <a href="../../../form/admin/transactiondata.jsp" class="btn btn-info" target="_blank">👀 View Transaction Page</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📈 Database Statistics</h3>
            <p>Statistik real-time dari database transaksi:</p>
            <button class="btn" onclick="loadDatabaseStats()">🔄 Load Database Stats</button>
            <div id="stats-container" style="display:none;">
                <div class="stats" id="stats-grid"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 Export Validation</h3>
            <p>Validasi komprehensif untuk memastikan export berfungsi dengan baik:</p>
            <button class="btn btn-success" onclick="runFullValidation()">🔍 Run Full Validation</button>
            <div id="validation-result" class="result" style="display:none;"></div>
        </div>
    </div>
    
    <script>
        function testExcelExport() {
            var resultDiv = document.getElementById('excel-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Testing Excel export...</p>';
            
            // Test dengan fetch untuk melihat response
            fetch('exportTransactionsExcel.jsp')
                .then(response => {
                    var contentType = response.headers.get('content-type');
                    var contentDisposition = response.headers.get('content-disposition');
                    
                    if (response.ok) {
                        return response.blob().then(blob => {
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = 
                                '<h4>✅ Excel Export Success!</h4>' +
                                '<p><strong>Content-Type:</strong> ' + contentType + '</p>' +
                                '<p><strong>Content-Disposition:</strong> ' + contentDisposition + '</p>' +
                                '<p><strong>File Size:</strong> ' + formatBytes(blob.size) + '</p>' +
                                '<p><strong>Status:</strong> <span class="status-badge status-success">XLSX file generated successfully</span></p>' +
                                '<p><a href="exportTransactionsExcel.jsp" target="_blank" class="btn btn-success">📊 Download Excel File</a></p>';
                        });
                    } else {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 
                        '<h4>❌ Excel Export Failed!</h4>' +
                        '<p><strong>Error:</strong> ' + error.message + '</p>' +
                        '<p><strong>Status:</strong> <span class="status-badge status-error">Export failed</span></p>';
                });
        }
        
        function testPDFExport() {
            var resultDiv = document.getElementById('pdf-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Testing PDF export...</p>';
            
            // Test dengan fetch untuk melihat response
            fetch('exportTransactionsPDF.jsp')
                .then(response => {
                    var contentType = response.headers.get('content-type');
                    var contentDisposition = response.headers.get('content-disposition');
                    
                    if (response.ok) {
                        return response.blob().then(blob => {
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = 
                                '<h4>✅ PDF Export Success!</h4>' +
                                '<p><strong>Content-Type:</strong> ' + contentType + '</p>' +
                                '<p><strong>Content-Disposition:</strong> ' + contentDisposition + '</p>' +
                                '<p><strong>File Size:</strong> ' + formatBytes(blob.size) + '</p>' +
                                '<p><strong>Status:</strong> <span class="status-badge status-success">PDF file generated successfully</span></p>' +
                                '<p><strong>Layout:</strong> A4 Landscape with centered table</p>' +
                                '<p><a href="exportTransactionsPDF.jsp" target="_blank" class="btn btn-info">📄 Download PDF File</a></p>';
                        });
                    } else {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 
                        '<h4>❌ PDF Export Failed!</h4>' +
                        '<p><strong>Error:</strong> ' + error.message + '</p>' +
                        '<p><strong>Status:</strong> <span class="status-badge status-error">Export failed</span></p>';
                });
        }
        
        function loadDatabaseStats() {
            var statsContainer = document.getElementById('stats-container');
            var statsGrid = document.getElementById('stats-grid');
            
            statsContainer.style.display = 'block';
            statsGrid.innerHTML = '<p>🔄 Loading database statistics...</p>';
            
            // Simulate loading stats (in real implementation, this would fetch from a stats endpoint)
            setTimeout(function() {
                statsGrid.innerHTML = 
                    '<div class="stat-card">' +
                    '<div class="stat-number">-</div>' +
                    '<div class="stat-label">Total Transaksi</div>' +
                    '</div>' +
                    '<div class="stat-card">' +
                    '<div class="stat-number">-</div>' +
                    '<div class="stat-label">Transaksi Selesai</div>' +
                    '</div>' +
                    '<div class="stat-card">' +
                    '<div class="stat-number">-</div>' +
                    '<div class="stat-label">Transaksi Menunggu</div>' +
                    '</div>' +
                    '<div class="stat-card">' +
                    '<div class="stat-number">-</div>' +
                    '<div class="stat-label">Transaksi Dibatalkan</div>' +
                    '</div>' +
                    '<div class="stat-card">' +
                    '<div class="stat-number">Rp -</div>' +
                    '<div class="stat-label">Total Nilai Selesai</div>' +
                    '</div>' +
                    '<div class="stat-card">' +
                    '<div class="stat-number">Rp -</div>' +
                    '<div class="stat-label">Total Nilai Menunggu</div>' +
                    '</div>';
            }, 1000);
        }
        
        function runFullValidation() {
            var resultDiv = document.getElementById('validation-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<p>🔄 Running full validation...</p>';
            
            var tests = [
                { name: 'Excel Export Response', test: () => fetch('exportTransactionsExcel.jsp') },
                { name: 'PDF Export Response', test: () => fetch('exportTransactionsPDF.jsp') }
            ];
            
            Promise.all(tests.map(test => 
                test.test().then(response => ({
                    name: test.name,
                    success: response.ok,
                    status: response.status,
                    contentType: response.headers.get('content-type')
                })).catch(error => ({
                    name: test.name,
                    success: false,
                    error: error.message
                }))
            )).then(results => {
                var successCount = results.filter(r => r.success).length;
                var totalCount = results.length;
                
                var html = '<h4>🔍 Validation Results</h4>';
                html += '<p><strong>Overall Status:</strong> ' + successCount + '/' + totalCount + ' tests passed</p>';
                
                results.forEach(result => {
                    html += '<div style="margin: 10px 0; padding: 10px; border-radius: 3px; ' + 
                           (result.success ? 'background: #d4edda; border-left: 3px solid #28a745;' : 'background: #f8d7da; border-left: 3px solid #dc3545;') + '">';
                    html += '<strong>' + result.name + ':</strong> ';
                    html += '<span class="status-badge ' + (result.success ? 'status-success">✅ PASS' : 'status-error">❌ FAIL') + '</span>';
                    if (result.success) {
                        html += '<br><small>Status: ' + result.status + ', Content-Type: ' + result.contentType + '</small>';
                    } else {
                        html += '<br><small>Error: ' + result.error + '</small>';
                    }
                    html += '</div>';
                });
                
                resultDiv.className = 'result ' + (successCount === totalCount ? 'success' : 'warning');
                resultDiv.innerHTML = html;
            });
        }
        
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
