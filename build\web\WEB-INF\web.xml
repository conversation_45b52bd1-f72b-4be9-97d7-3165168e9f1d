<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee
                             https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         version="6.0">

    <display-name>Arqeta - Jasa Pembuatan UI Web dan Mobile</display-name>
    <description>
        Arqeta adalah platform jasa pembuatan UI Web dan Mobile yang menyediakan
        layanan profesional untuk kebutuhan desain dan pengembangan antarmuka pengguna.
    </description>

    <!-- ========================================= -->
    <!-- CHARACTER ENCODING FILTER - UTF-8 SUPPORT -->
    <!-- ========================================= -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>org.apache.catalina.filters.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>ignore</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>
    
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- ========================================= -->
    <!-- CORS FILTER - CROSS-ORIGIN SUPPORT -->
    <!-- ========================================= -->
    <filter>
        <filter-name>CorsFilter</filter-name>
        <filter-class>org.apache.catalina.filters.CorsFilter</filter-class>
        <init-param>
            <param-name>cors.allowed.origins</param-name>
            <param-value>*</param-value>
        </init-param>
        <init-param>
            <param-name>cors.allowed.methods</param-name>
            <param-value>GET,POST,HEAD,OPTIONS,PUT,DELETE</param-value>
        </init-param>
        <init-param>
            <param-name>cors.allowed.headers</param-name>
            <param-value>Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers</param-value>
        </init-param>
        <init-param>
            <param-name>cors.exposed.headers</param-name>
            <param-value>Access-Control-Allow-Origin,Access-Control-Allow-Credentials</param-value>
        </init-param>
        <init-param>
            <param-name>cors.support.credentials</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- ========================================= -->
    <!-- JSP CONFIGURATION - UTF-8 PAGE ENCODING -->
    <!-- ========================================= -->
    <jsp-config>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <page-encoding>UTF-8</page-encoding>
            <scripting-invalid>false</scripting-invalid>
        </jsp-property-group>
    </jsp-config>

    <!-- ========================================= -->
    <!-- SERVLET CONFIGURATIONS - FILE UPLOAD SUPPORT -->
    <!-- ========================================= -->
    
    <!-- Services Data Management -->
    <servlet>
        <servlet-name>servicesdata</servlet-name>
        <jsp-file>/form/admin/servicesdata.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>servicesdata</servlet-name>
        <url-pattern>/form/admin/servicesdata.jsp</url-pattern>
    </servlet-mapping>

    <!-- Portfolio Data Management -->
    <servlet>
        <servlet-name>portfoliodata</servlet-name>
        <jsp-file>/form/admin/portfoliodata.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>portfoliodata</servlet-name>
        <url-pattern>/form/admin/portfoliodata.jsp</url-pattern>
    </servlet-mapping>

    <!-- Blog Data Management -->
    <servlet>
        <servlet-name>blogdata</servlet-name>
        <jsp-file>/form/admin/blogdata.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>blogdata</servlet-name>
        <url-pattern>/form/admin/blogdata.jsp</url-pattern>
    </servlet-mapping>

    <!-- Add Service Process -->
    <servlet>
        <servlet-name>addService</servlet-name>
        <jsp-file>/process/admin/addService.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>addService</servlet-name>
        <url-pattern>/process/admin/addService.jsp</url-pattern>
    </servlet-mapping>

    <!-- Add Portfolio Process -->
    <servlet>
        <servlet-name>addPortfolio</servlet-name>
        <jsp-file>/process/admin/addPortfolio.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>addPortfolio</servlet-name>
        <url-pattern>/process/admin/addPortfolio.jsp</url-pattern>
    </servlet-mapping>

    <!-- Add Blog Process -->
    <servlet>
        <servlet-name>addBlog</servlet-name>
        <jsp-file>/process/admin/addBlog.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>

    <servlet-mapping>
        <servlet-name>addBlog</servlet-name>
        <url-pattern>/process/admin/addBlog.jsp</url-pattern>
    </servlet-mapping>

    <!-- Edit Service Process -->
    <servlet>
        <servlet-name>editService</servlet-name>
        <jsp-file>/process/admin/editService.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>

    <servlet-mapping>
        <servlet-name>editService</servlet-name>
        <url-pattern>/process/admin/editService.jsp</url-pattern>
    </servlet-mapping>

    <!-- Edit Portfolio Process -->
    <servlet>
        <servlet-name>editPortfolio</servlet-name>
        <jsp-file>/process/admin/editPortfolio.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>

    <servlet-mapping>
        <servlet-name>editPortfolio</servlet-name>
        <url-pattern>/process/admin/editPortfolio.jsp</url-pattern>
    </servlet-mapping>

    <!-- Edit Blog Process -->
    <servlet>
        <servlet-name>editBlog</servlet-name>
        <jsp-file>/process/admin/editBlog.jsp</jsp-file>
        <multipart-config>
            <location>C:/temp</location>
            <max-file-size>10485760</max-file-size> <!-- 10MB -->
            <max-request-size>20971520</max-request-size> <!-- 20MB -->
            <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
        </multipart-config>
    </servlet>

    <servlet-mapping>
        <servlet-name>editBlog</servlet-name>
        <url-pattern>/process/admin/editBlog.jsp</url-pattern>
    </servlet-mapping>

    <!-- Manage Transaction Process -->
    <servlet>
        <servlet-name>manageTransaction</servlet-name>
        <jsp-file>/process/admin/manageTransaction.jsp</jsp-file>
    </servlet>

    <servlet-mapping>
        <servlet-name>manageTransaction</servlet-name>
        <url-pattern>/process/admin/manageTransaction.jsp</url-pattern>
    </servlet-mapping>

    <!-- Add to Cart Process -->
    <servlet>
        <servlet-name>addToCart</servlet-name>
        <jsp-file>/process/user/addToCartNew.jsp</jsp-file>
    </servlet>

    <servlet-mapping>
        <servlet-name>addToCart</servlet-name>
        <url-pattern>/process/user/addToCartNew.jsp</url-pattern>
    </servlet-mapping>

    <!-- ========================================= -->
    <!-- MIME TYPE MAPPINGS -->
    <!-- ========================================= -->
    <mime-mapping>
        <extension>css</extension>
        <mime-type>text/css</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>js</extension>
        <mime-type>application/javascript</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>svg</extension>
        <mime-type>image/svg+xml</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>png</extension>
        <mime-type>image/png</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>jpg</extension>
        <mime-type>image/jpeg</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>jpeg</extension>
        <mime-type>image/jpeg</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>gif</extension>
        <mime-type>image/gif</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>webp</extension>
        <mime-type>image/webp</mime-type>
    </mime-mapping>

    <mime-mapping>
        <extension>json</extension>
        <mime-type>application/json</mime-type>
    </mime-mapping>

    <!-- ========================================= -->
    <!-- ERROR PAGE MAPPINGS -->
    <!-- ========================================= -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.jsp</location>
    </error-page>
    
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.jsp</location>
    </error-page>
    
    <error-page>
        <exception-type>java.lang.Exception</exception-type>
        <location>/error/error.jsp</location>
    </error-page>

    <!-- ========================================= -->
    <!-- SECURITY CONSTRAINTS -->
    <!-- ========================================= -->
    <security-constraint>
        <display-name>Admin Pages Protection</display-name>
        <web-resource-collection>
            <web-resource-name>Admin Dashboard</web-resource-name>
            <url-pattern>/dashboardadmin.jsp</url-pattern>
            <url-pattern>/form/admin/*</url-pattern>
            <url-pattern>/process/admin/*</url-pattern>
            <http-method>GET</http-method>
            <http-method>POST</http-method>
        </web-resource-collection>
        <!-- Note: Authentication will be handled by session management in JSP -->
    </security-constraint>

    <!-- ========================================= -->
    <!-- SESSION CONFIGURATION -->
    <!-- ========================================= -->
    <session-config>
        <session-timeout>30</session-timeout> <!-- 30 minutes -->
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure> <!-- Set to true in production with HTTPS -->
        </cookie-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>

    <!-- ========================================= -->
    <!-- WELCOME FILE LIST -->
    <!-- ========================================= -->
    <welcome-file-list>
        <welcome-file>home.jsp</welcome-file>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

</web-app>
